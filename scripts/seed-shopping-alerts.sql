-- Shopping Lists, Purchases, and Alert Configuration Seed Data
-- This file contains shopping lists, purchase history, and alert configurations

-- ============================================================================
-- SHOPPING LISTS - Create realistic shopping scenarios
-- ============================================================================

-- Johns weekly shopping list
INSERT INTO shopping_lists (shopping_list_id, pantry_id, name, description, status, created_by) 
VALUES ('90000000-0000-0000-0000-************', '********-0000-0000-0000-************', 'Weekly Groceries', 'Regular weekly grocery shopping', 'active', '22222222-2222-2222-2222-222222222222');

-- Family monthly shopping list
INSERT INTO shopping_lists (shopping_list_id, pantry_id, name, description, status, created_by) 
VALUES ('90000000-0000-0000-0000-************', '********-0000-0000-0000-000000000004', 'Monthly Stock Up', 'Monthly bulk shopping for family', 'active', '77777777-7777-7777-7777-************');

-- Restaurant supply list
INSERT INTO shopping_lists (shopping_list_id, pantry_id, name, description, status, created_by) 
VALUES ('90000000-0000-0000-0000-************', '********-0000-0000-0000-************', 'Restaurant Supplies', 'Weekly restaurant ingredient order', 'completed', '********-6666-6666-6666-************');

-- Office snack list
INSERT INTO shopping_lists (shopping_list_id, pantry_id, name, description, status, created_by) 
VALUES ('90000000-0000-0000-0000-000000000004', '********-0000-0000-0000-000000000006', 'Office Snacks', 'Monthly office snack restock', 'active', '11111111-1111-1111-1111-111111111111');

-- ============================================================================
-- SHOPPING LIST ITEMS - Add items to shopping lists
-- ============================================================================

-- Johns weekly shopping list items
INSERT INTO shopping_list_items (shopping_list_item_id, shopping_list_id, product_variant_id, quantity, unit_of_measure_id, notes, is_purchased, estimated_price) 
VALUES 
    ('91000000-0000-0000-0000-************', '90000000-0000-0000-0000-************', '********-0000-0000-0000-************', 1, (SELECT unit_id FROM units_of_measure WHERE symbol = 'gal'), 'Need fresh milk', false, 4.99),
    ('91000000-0000-0000-0000-************', '90000000-0000-0000-0000-************', '********-0000-0000-0000-************', 1, (SELECT unit_id FROM units_of_measure WHERE symbol = 'dz'), 'Running low on eggs', false, 3.49),
    ('91000000-0000-0000-0000-************', '90000000-0000-0000-0000-************', '********-0000-0000-0000-000000000008', 1, (SELECT unit_id FROM units_of_measure WHERE symbol = 'lb'), 'For smoothies', false, 2.99);

-- Add free text items to Johns list
INSERT INTO shopping_list_items (shopping_list_item_id, shopping_list_id, item_name, quantity, unit_of_measure_id, notes, is_purchased, estimated_price) 
VALUES 
    ('91000000-0000-0000-0000-000000000004', '90000000-0000-0000-0000-************', 'Bread', 2, (SELECT unit_id FROM units_of_measure WHERE symbol = 'pc'), 'Whole wheat bread', false, 5.98),
    ('91000000-0000-0000-0000-************', '90000000-0000-0000-0000-************', 'Avocados', 4, (SELECT unit_id FROM units_of_measure WHERE symbol = 'pc'), 'For guacamole', false, 3.96);

-- Family monthly shopping list items
INSERT INTO shopping_list_items (shopping_list_item_id, shopping_list_id, product_variant_id, quantity, unit_of_measure_id, notes, is_purchased, estimated_price) 
VALUES 
    ('91000000-0000-0000-0000-000000000006', '90000000-0000-0000-0000-************', '********-0000-0000-0000-000000000013', 5, (SELECT unit_id FROM units_of_measure WHERE symbol = 'lb'), 'Bulk rice purchase', false, 19.95),
    ('91000000-0000-0000-0000-000000000007', '90000000-0000-0000-0000-************', '********-0000-0000-0000-000000000014', 12, (SELECT unit_id FROM units_of_measure WHERE symbol = 'can'), 'Stock up on beans', false, 23.88),
    ('91000000-0000-0000-0000-000000000008', '90000000-0000-0000-0000-************', '********-0000-0000-0000-000000000015', 3, (SELECT unit_id FROM units_of_measure WHERE symbol = 'bag'), 'Coffee for the month', false, 26.97);

-- Restaurant supply list items (completed purchases)
INSERT INTO shopping_list_items (shopping_list_item_id, shopping_list_id, product_variant_id, quantity, unit_of_measure_id, notes, is_purchased, estimated_price, actual_price) 
VALUES 
    ('91000000-0000-0000-0000-000000000009', '90000000-0000-0000-0000-************', '********-0000-0000-0000-************', 25, (SELECT unit_id FROM units_of_measure WHERE symbol = 'lb'), 'Weekly chicken order', true, 125.00, 118.75),
    ('91000000-0000-0000-0000-000000000010', '90000000-0000-0000-0000-************', '********-0000-0000-0000-000000000011', 5, (SELECT unit_id FROM units_of_measure WHERE symbol = 'btl'), 'Premium olive oil', true, 64.95, 59.99);

-- Office snack list items
INSERT INTO shopping_list_items (shopping_list_item_id, shopping_list_id, product_variant_id, quantity, unit_of_measure_id, notes, is_purchased, estimated_price) 
VALUES 
    ('91000000-0000-0000-0000-000000000011', '90000000-0000-0000-0000-000000000004', '********-0000-0000-0000-000000000015', 2, (SELECT unit_id FROM units_of_measure WHERE symbol = 'bag'), 'Office coffee supply', false, 17.98),
    ('91000000-0000-0000-0000-000000000012', '90000000-0000-0000-0000-000000000004', '********-0000-0000-0000-000000000017', 5, (SELECT unit_id FROM units_of_measure WHERE symbol = 'pc'), 'Healthy office snacks', false, 34.95);

-- ============================================================================
-- PURCHASES - Create purchase history for different scenarios
-- ============================================================================

-- Johns recent grocery purchase
INSERT INTO purchases (purchase_id, pantry_id, store_id, purchased_by_user_id, purchase_date, total_amount, payment_method, receipt_image_url, notes) 
VALUES ('********-0000-0000-0000-************', '********-0000-0000-0000-************', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '22222222-2222-2222-2222-222222222222', '2025-06-10', 45.67, 'credit_card', 'https://example.com/receipts/john-grocery-1.jpg', 'Weekly grocery run');

-- Family bulk purchase
INSERT INTO purchases (purchase_id, pantry_id, store_id, purchased_by_user_id, purchase_date, total_amount, payment_method, receipt_image_url, notes) 
VALUES ('********-0000-0000-0000-************', '********-0000-0000-0000-000000000004', 'dddddddd-dddd-dddd-dddd-dddddddddddd', '77777777-7777-7777-7777-************', '2025-06-05', 156.89, 'debit_card', 'https://example.com/receipts/family-costco-1.jpg', 'Monthly Costco run');

-- Restaurant supply purchase
INSERT INTO purchases (purchase_id, pantry_id, store_id, purchased_by_user_id, purchase_date, total_amount, payment_method, receipt_image_url, notes) 
VALUES ('********-0000-0000-0000-************', '********-0000-0000-0000-************', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '********-6666-6666-6666-************', '2025-06-12', 287.45, 'business_account', 'https://example.com/receipts/restaurant-supply-1.jpg', 'Weekly ingredient order');

-- ============================================================================
-- PURCHASE ITEMS - Detail items in each purchase
-- ============================================================================

-- Johns grocery purchase items
INSERT INTO purchase_items (purchase_item_id, purchase_id, product_variant_id, quantity, unit_of_measure_id, unit_price, total_price, expiration_date) 
VALUES 
    ('********-0000-0000-0000-************', '********-0000-0000-0000-************', '********-0000-0000-0000-************', 1, (SELECT unit_id FROM units_of_measure WHERE symbol = 'gal'), 4.99, 4.99, '2025-06-20'),
    ('********-0000-0000-0000-************', '********-0000-0000-0000-************', '********-0000-0000-0000-************', 1, (SELECT unit_id FROM units_of_measure WHERE symbol = 'dz'), 3.49, 3.49, '2025-06-25'),
    ('********-0000-0000-0000-************', '********-0000-0000-0000-************', '********-0000-0000-0000-************', 1, (SELECT unit_id FROM units_of_measure WHERE symbol = 'oz'), 5.99, 5.99, '2025-07-15'),
    ('********-0000-0000-0000-000000000004', '********-0000-0000-0000-************', '********-0000-0000-0000-************', 1, (SELECT unit_id FROM units_of_measure WHERE symbol = 'lb'), 8.99, 8.99, '2025-06-18');

-- Family bulk purchase items
INSERT INTO purchase_items (purchase_item_id, purchase_id, product_variant_id, quantity, unit_of_measure_id, unit_price, total_price, expiration_date) 
VALUES 
    ('********-0000-0000-0000-************', '********-0000-0000-0000-************', '********-0000-0000-0000-000000000013', 3, (SELECT unit_id FROM units_of_measure WHERE symbol = 'lb'), 3.99, 11.97, '2026-08-01'),
    ('********-0000-0000-0000-000000000006', '********-0000-0000-0000-************', '********-0000-0000-0000-000000000014', 8, (SELECT unit_id FROM units_of_measure WHERE symbol = 'can'), 1.99, 15.92, '2026-12-01'),
    ('********-0000-0000-0000-000000000007', '********-0000-0000-0000-************', '********-0000-0000-0000-000000000015', 2, (SELECT unit_id FROM units_of_measure WHERE symbol = 'bag'), 8.99, 17.98, '2026-08-01');

-- Restaurant supply purchase items
INSERT INTO purchase_items (purchase_item_id, purchase_id, product_variant_id, quantity, unit_of_measure_id, unit_price, total_price, expiration_date) 
VALUES 
    ('********-0000-0000-0000-000000000008', '********-0000-0000-0000-************', '********-0000-0000-0000-************', 25, (SELECT unit_id FROM units_of_measure WHERE symbol = 'lb'), 4.50, 112.50, '2025-06-20'),
    ('********-0000-0000-0000-000000000009', '********-0000-0000-0000-************', '********-0000-0000-0000-000000000006', 15, (SELECT unit_id FROM units_of_measure WHERE symbol = 'lb'), 4.00, 60.00, '2025-06-18'),
    ('********-0000-0000-0000-000000000010', '********-0000-0000-0000-************', '********-0000-0000-0000-000000000011', 4, (SELECT unit_id FROM units_of_measure WHERE symbol = 'btl'), 12.99, 51.96, '2026-12-31');

-- ============================================================================
-- ALERT CONFIGURATIONS - Set up expiration tracking alerts
-- ============================================================================

-- Johns global alert configuration
INSERT INTO alert_configurations (id, user_id, pantry_id, enabled, warning_days, alert_days, critical_days, channels, quiet_hours, category_filters, min_value) 
VALUES ('94000000-0000-0000-0000-************', '22222222-2222-2222-2222-222222222222', NULL, true, 7, 3, 1, '["in_app", "email"]', '{"start": "22:00", "end": "08:00"}', '[]', 5.00);

-- Johns kitchen-specific alert configuration
INSERT INTO alert_configurations (id, user_id, pantry_id, enabled, warning_days, alert_days, critical_days, channels, quiet_hours, category_filters, min_value) 
VALUES ('94000000-0000-0000-0000-************', '22222222-2222-2222-2222-222222222222', '********-0000-0000-0000-************', true, 5, 2, 1, '["in_app"]', NULL, '[]', 3.00);

-- Family kitchen alert configuration
INSERT INTO alert_configurations (id, user_id, pantry_id, enabled, warning_days, alert_days, critical_days, channels, quiet_hours, category_filters, min_value) 
VALUES ('94000000-0000-0000-0000-************', '77777777-7777-7777-7777-************', '********-0000-0000-0000-000000000004', true, 10, 5, 2, '["in_app", "email"]', '{"start": "21:00", "end": "07:00"}', '[]', 2.00);

-- Restaurant alert configuration (more aggressive)
INSERT INTO alert_configurations (id, user_id, pantry_id, enabled, warning_days, alert_days, critical_days, channels, quiet_hours, category_filters, min_value) 
VALUES ('94000000-0000-0000-0000-000000000004', '********-6666-6666-6666-************', '********-0000-0000-0000-************', true, 3, 1, 1, '["in_app", "email"]', NULL, '[]', 10.00);

-- ============================================================================
-- NOTIFICATIONS - Create sample notification history
-- ============================================================================

-- Recent expiration warning for John
INSERT INTO notifications (id, user_id, pantry_id, type, channel, priority, title, message, data, status, scheduled_at, sent_at) 
VALUES ('95000000-0000-0000-0000-************', '22222222-2222-2222-2222-222222222222', '********-0000-0000-0000-************', 'expiration_warning', 'in_app', 'medium', 'Items Expiring Soon', 'You have 2 items expiring in the next 7 days in Johns Kitchen', '{"item_count": 2, "pantry_name": "Johns Kitchen"}', 'sent', '2025-06-12 09:00:00+00', '2025-06-12 09:00:15+00');

-- Critical expiration alert for restaurant
INSERT INTO notifications (id, user_id, pantry_id, type, channel, priority, title, message, data, status, scheduled_at, sent_at) 
VALUES ('95000000-0000-0000-0000-************', '********-6666-6666-6666-************', '********-0000-0000-0000-************', 'expiration_critical', 'email', 'critical', 'URGENT: Items Expiring Today', 'Critical: 3 items are expiring today in Restaurant Pantry', '{"item_count": 3, "pantry_name": "Restaurant Pantry"}', 'sent', '2025-06-12 06:00:00+00', '2025-06-12 06:00:30+00');

-- Pending notification for family
INSERT INTO notifications (id, user_id, pantry_id, type, channel, priority, title, message, data, status, scheduled_at) 
VALUES ('95000000-0000-0000-0000-************', '77777777-7777-7777-7777-************', '********-0000-0000-0000-000000000004', 'expiration_alert', 'in_app', 'high', 'Items Expiring in 3 Days', 'You have 1 item expiring in 3 days in Family Kitchen', '{"item_count": 1, "pantry_name": "Family Kitchen"}', 'pending', '2025-06-13 08:00:00+00');
