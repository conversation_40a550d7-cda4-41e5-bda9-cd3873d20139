# Enhanced Error Handling and Logging

This document describes the improvements made to error handling and logging in the Pantry Pal application to make errors easier to understand and debug.

## Problem Statement

The original error handling system had several issues:

1. **Generic error messages**: Errors like "Cannot GET /api/v1/inventory/..." didn't provide specific details
2. **Limited context**: Error logs lacked sufficient contextual information for debugging
3. **No error categorization**: All errors were logged similarly without distinguishing types
4. **Missing stack traces**: No detailed stack traces for debugging
5. **Poor error correlation**: Difficult to trace errors across different layers

## Solution Overview

The enhanced error handling system provides:

1. **Comprehensive Error Middleware**: Detailed error handling with categorization
2. **Structured Error Logging**: Enhanced logging with context and categorization
3. **Error Context Enhancement**: Rich contextual information for debugging
4. **Stack Trace Support**: Optional stack traces for development
5. **Error Helper Functions**: Consistent error handling across handlers

## Components

### 1. Enhanced Error Handler Middleware (`error_handler.go`)

**Location**: `internal/infra/web/middleware/error_handler.go`

**Features**:
- Comprehensive error context building
- Error categorization (authentication, validation, database, etc.)
- Stack trace support for development
- Detailed request/response logging
- Sensitive header filtering

**Configuration**:
```go
ErrorHandler(ErrorHandlerConfig{
    Logger:               logger,
    EnableStackTrace:     true,  // Development only
    EnableDetailedErrors: true,  // Development only
    MaxStackTraceDepth:   15,
})
```

### 2. Error Context Middleware (`error_context.go`)

**Location**: `internal/infra/web/middleware/error_context.go`

**Features**:
- Enhances errors with request context
- Optional request/response body logging
- Processing time tracking
- Route information capture

**Configuration**:
```go
ErrorContext(ErrorContextConfig{
    Logger:                   logger,
    EnableRequestBodyLogging: true,  // Development only
    MaxRequestBodySize:       2048,  // 2KB
    EnableResponseBodyLogging: true, // Development only
    MaxResponseBodySize:      2048,  // 2KB
})
```

### 3. Enhanced Logger Methods (`logger.go`)

**Location**: `internal/infra/logger/logger.go`

**New Methods**:
- `LogErrorWithCategory()`: Categorized error logging
- `LogValidationError()`: Validation-specific errors
- `LogBusinessError()`: Business logic errors
- `LogSystemError()`: System-level errors
- `LogDatabaseError()`: Database-specific errors
- `LogExternalServiceError()`: External service errors
- `LogAuthenticationError()`: Authentication errors
- `LogAuthorizationError()`: Authorization errors
- `LogPerformanceIssue()`: Performance problems
- `LogRateLimitError()`: Rate limiting errors

### 4. Error Helper Functions (`error_helper.go`)

**Location**: `internal/infra/web/handler/error_helper.go`

**Features**:
- Consistent error logging across handlers
- Automatic error categorization
- Context building helpers
- Specialized logging functions

**Usage Example**:
```go
return LogAndReturnError(c, h.logger, err, ErrorLogContext{
    Operation:   "create_inventory_item",
    UserID:      userID,
    Resource:    "inventory_item",
    RequestData: req,
    Additional: map[string]interface{}{
        "pantry_id": pantryID,
    },
})
```

## Error Categories

The system categorizes errors into the following types:

1. **Authentication**: Invalid credentials, expired tokens
2. **Authorization**: Forbidden access, insufficient permissions
3. **Validation**: Invalid input, missing fields
4. **Not Found**: Resource not found
5. **Conflict**: Resource conflicts, business rule violations
6. **Business Logic**: Domain-specific rule violations
7. **Database**: Database connection, query errors
8. **External Service**: Third-party service failures
9. **Rate Limit**: Rate limiting violations
10. **System**: Internal server errors, timeouts
11. **Performance**: Slow operations, threshold violations

## Enhanced Error Context

Each error now includes comprehensive context:

### Request Context
- HTTP method, path, and original URL
- User agent, client IP, protocol
- Query parameters and route parameters
- Request headers (excluding sensitive ones)
- Request body (in development, with size limits)

### Processing Context
- Processing time in milliseconds
- Route information (path pattern, method)
- User ID and request ID for correlation

### Error Context
- Error category and code
- HTTP status code
- Error details and underlying causes
- Stack trace (in development)

## Configuration

### Development Mode
- Stack traces enabled
- Detailed error responses
- Request/response body logging
- Enhanced debug headers

### Production Mode
- Stack traces disabled
- Minimal error responses
- No body logging
- Security-focused logging

## Usage Examples

### Handler Error Logging
```go
// Before (generic)
if err != nil {
    h.logger.LogError(err, "Failed to create item", map[string]interface{}{
        "user_id": userID,
    })
    return ErrorResponse(c, err)
}

// After (enhanced)
if err != nil {
    return LogAndReturnError(c, h.logger, err, ErrorLogContext{
        Operation:   "create_inventory_item",
        UserID:      userID,
        Resource:    "inventory_item",
        RequestData: req,
    })
}
```

### Specialized Error Logging
```go
// Validation errors
return LogValidationError(c, h.logger, err, ErrorLogContext{
    Operation: "validate_input",
    UserID:    userID,
})

// Not found errors
return LogNotFoundError(c, h.logger, err, "inventory_item", itemID, ErrorLogContext{
    UserID: userID,
})

// Authorization errors
return LogAuthorizationError(c, h.logger, err, "inventory_item", ErrorLogContext{
    UserID: userID,
})
```

## Benefits

1. **Easier Debugging**: Rich context and categorization make issues easier to identify
2. **Better Monitoring**: Structured logs enable better alerting and monitoring
3. **Improved Security**: Sensitive data filtering and controlled error exposure
4. **Performance Insights**: Processing time tracking and performance issue detection
5. **Consistent Logging**: Standardized error handling across all handlers
6. **Development Productivity**: Enhanced debugging information in development mode

## Migration Guide

To use the enhanced error handling in existing handlers:

1. Replace `ErrorResponse(c, err)` with `LogAndReturnError(c, h.logger, err, context)`
2. Use specialized helper functions for common error types
3. Provide meaningful operation names and context
4. Include relevant request data for debugging

The enhanced error handling system is backward compatible and can be adopted gradually across the application.
