# Purchase History Management

## Overview
This document outlines the requirements for the Purchase History Management feature, enabling users to record purchase transactions, link them to inventory, and manage frequently visited stores.

## Key Features & Requirements

### Record Purchase Transactions
- **Record Details**: As a user, I can record details of a purchase transaction including the date, total amount, currency, and the store name. Store names can be selected from a system-curated list or entered as free text for a user's private list of stores.
- **Add Purchase Items**: As a user, I can add individual items to a purchase record, specifying the product variant, quantity bought, and price per unit.
- **Link to Inventory**: As a user, I can link items recorded in a purchase directly to new inventory items being added to the pantry.

### Store Management
- **Manage Stores**: As a user, I can manage a list of frequently visited stores (user-specific list for quick selection). Globally curated stores will be managed by system administrators.

## Related Entities
- [`Purchase`](internal/core/domain/purchase.go) (conceptual)
- [`PurchaseItem`](internal/core/domain/purchase.go) (conceptual)
- [`Store`](internal/core/domain/purchase.go) (conceptual)

## API Endpoints (Conceptual)
- `POST /pantries/{pantryId}/purchases` (Create purchase)
- `GET /pantries/{pantryId}/purchases` (List purchases)
- `GET /pantries/{pantryId}/purchases/{purchaseId}`
- `PUT /pantries/{pantryId}/purchases/{purchaseId}`
- `DELETE /pantries/{pantryId}/purchases/{purchaseId}`
- `POST /pantries/{pantryId}/purchases/{purchaseId}/items` (Add item to purchase)
- `POST /pantries/{pantryId}/purchases/{purchaseId}/link-to-inventory` (Link purchase items to inventory)
- `POST /stores` (Create store)
- `GET /stores` (List stores)
- `GET /stores/{storeId}`
- `PUT /stores/{storeId}`
- `DELETE /stores/{storeId}`

## Implementation Notes
- Integration with Product Catalog and Unit of Measure Management.
- Consideration for handling global vs. user-specific stores.
- Database migrations for purchase and store tables are needed.