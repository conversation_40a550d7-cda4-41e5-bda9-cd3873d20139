# Pantry Pal Database Schema for PostgreSQL

*   **Version:** 1.0
*   **Date:** October 28, 2023

## 1. Overview

This document details the PostgreSQL database schema for the Pantry Pal application. It includes table definitions, column types, constraints, indexes, and relationships. All primary keys are UUIDs. Timestamps (`created_at`, `updated_at`) are `TIMESTAMPTZ` for timezone awareness. Soft deletion is implemented using a `deleted_at TIMESTAMPTZ` column.

## 2. Setup

```sql
-- Enable UUID generation if not already enabled on the server/database
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
```

## 3. ENUM Types

```sql
CREATE TYPE pantry_role AS ENUM ('owner', 'admin', 'editor', 'viewer');
CREATE TYPE pantry_membership_status AS ENUM ('pending_invitation', 'active', 'inactive', 'removed');
CREATE TYPE product_variant_packaging_type AS ENUM ('single', 'bulk', 'multi-pack', 'other');
CREATE TYPE unit_of_measure_type AS ENUM ('volume', 'weight', 'count', 'length', 'area', 'other');
CREATE TYPE inventory_item_status AS ENUM ('in_stock', 'low_stock', 'out_of_stock', 'expired', 'used_up');
CREATE TYPE shopping_list_status AS ENUM ('active', 'completed', 'archived');
CREATE TYPE inventory_adjustment_type AS ENUM ('spoilage', 'loss', 'manual_correction', 'transfer_out', 'transfer_in');
CREATE TYPE pantry_setting_data_type AS ENUM ('string', 'number', 'boolean', 'json');
CREATE TYPE notification_type AS ENUM ('low_stock', 'expired_item', 'invitation_received', 'recipe_shared', 'item_added', 'general_alert');
```

## 4. Table Definitions

### 4.1. `users`
Stores information about application users.

```sql
CREATE TABLE users (
    user_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    profile_picture_url VARCHAR(255),
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ -- For soft deletion
);
CREATE INDEX idx_users_email ON users(email); -- For login
CREATE INDEX idx_users_deleted_at ON users(deleted_at);
```

### 4.2. `refresh_tokens`
Stores cryptographic hashes of JWT refresh tokens for revocation and session management.

```sql
CREATE TABLE refresh_tokens (
    refresh_token_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL UNIQUE, -- Hashed refresh token string
    expires_at TIMESTAMPTZ NOT NULL,
    is_revoked BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
    -- No updated_at or deleted_at needed for this table usually. Revocation is logical.
);
CREATE INDEX idx_refresh_tokens_user_id ON refresh_tokens(user_id);
CREATE INDEX idx_refresh_tokens_expires_at ON refresh_tokens(expires_at);
```

### 4.3. `pantries`
Represents individual pantry spaces owned by users.

```sql
CREATE TABLE pantries (
    pantry_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    owner_user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE RESTRICT, -- Prevent deleting user if they own pantries; transfer ownership first
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ,
    UNIQUE (owner_user_id, name, deleted_at) -- A user cannot own two non-deleted pantries with the same name.
                                             -- For truly unique even with soft delete, handle in application or make name globally unique per user if needed.
                                             -- Simpler: UNIQUE (owner_user_id, name) if soft deleted pantries don't count for uniqueness.
);
CREATE INDEX idx_pantries_owner_user_id ON pantries(owner_user_id);
CREATE INDEX idx_pantries_deleted_at ON pantries(deleted_at);
```

### 4.4. `pantry_memberships`
Manages user access and roles within specific pantries.

```sql
CREATE TABLE pantry_memberships (
    pantry_membership_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    pantry_id UUID NOT NULL REFERENCES pantries(pantry_id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    role pantry_role NOT NULL DEFAULT 'editor',
    joined_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    invited_by_user_id UUID REFERENCES users(user_id) ON DELETE SET NULL, -- User who invited this member
    status pantry_membership_status NOT NULL DEFAULT 'active',
    deleted_at TIMESTAMPTZ,
    -- To ensure a user has only one *active* (non-deleted, status='active' or 'pending_invitation') membership per pantry:
    -- This is complex for a UNIQUE constraint. Best handled by application logic or a partial unique index if DB supports it well.
    -- Simple uniqueness for now:
    UNIQUE (pantry_id, user_id) -- This allows only one record per user per pantry regardless of status/deleted_at.
                               -- Adjust if multiple (e.g., historical removed) records are needed per user/pantry.
);
CREATE INDEX idx_pantry_memberships_pantry_id ON pantry_memberships(pantry_id);
CREATE INDEX idx_pantry_memberships_user_id ON pantry_memberships(user_id);
CREATE INDEX idx_pantry_memberships_status ON pantry_memberships(status);
CREATE INDEX idx_pantry_memberships_deleted_at ON pantry_memberships(deleted_at);
```

### 4.5. `categories`
Categorizes products (e.g., "Dairy", "Grains").

```sql
CREATE TABLE categories (
    category_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    parent_category_id UUID REFERENCES categories(category_id) ON DELETE SET NULL, -- For hierarchical categories
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ,
    -- Uniqueness: Name should be unique among siblings (same parent_category_id)
    -- or unique if parent_category_id is NULL (top-level).
    -- This requires a more complex constraint or application logic.
    -- For simplicity, a global unique name might be enforced initially or scoped unique name for top-level.
    UNIQUE (name, parent_category_id) -- This makes name unique under a specific parent. NULLs are tricky with UNIQUE.
                                      -- Consider making name globally UNIQUE or manage in app.
                                      -- For now, let's assume global unique name for simplicity of schema.
    -- UNIQUE (name) -- Simpler, but less flexible for subcategories with same name under different parents.
);
CREATE INDEX idx_categories_parent_category_id ON categories(parent_category_id);
CREATE INDEX idx_categories_name ON categories(name); -- If globally unique or frequently searched by name
CREATE INDEX idx_categories_deleted_at ON categories(deleted_at);
```
*(For `categories.name` uniqueness: A common approach is to enforce global uniqueness for top-level categories (parent_id IS NULL) and uniqueness of name *within* a parent for subcategories. This often requires application-level checks or more complex DB constraints if supported.)*

### 4.6. `products`
General definition of a product (e.g., "Milk", "Apples").

```sql
CREATE TABLE products (
    product_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL, -- General name (e.g., "Milk")
    description TEXT,
    category_id UUID NOT NULL REFERENCES categories(category_id) ON DELETE RESTRICT, -- Don't delete category if products exist
    brand VARCHAR(100), -- General brand (e.g., "Horizon Organic")
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ,
    -- A product name is unique within a specific brand and category.
    -- If brand is NULL, then (name, category_id) must be unique.
    -- This requires a more complex unique index or application logic.
    -- For simplicity, assuming a composite unique key:
    UNIQUE (name, brand, category_id) -- Handles NULLs in `brand` correctly in PostgreSQL for uniqueness
);
CREATE INDEX idx_products_category_id ON products(category_id);
CREATE INDEX idx_products_brand ON products(brand);
CREATE INDEX idx_products_deleted_at ON products(deleted_at);
```

### 4.7. `unit_of_measures`
Defines various units of measurement.

```sql
CREATE TABLE unit_of_measures (
    unit_of_measure_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) UNIQUE NOT NULL, -- Full name (e.g., "Gallon")
    abbreviation VARCHAR(10) UNIQUE NOT NULL, -- Abbreviation (e.g., "gal")
    type unit_of_measure_type NOT NULL, -- Classification of the unit
    is_base_unit BOOLEAN NOT NULL DEFAULT FALSE, -- True if a standard base unit for its type
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);
CREATE INDEX idx_unit_of_measures_type ON unit_of_measures(type);
CREATE INDEX idx_unit_of_measures_deleted_at ON unit_of_measures(deleted_at);
```

### 4.8. `product_variants`
Specific versions of a product (e.g., "Whole Milk - Gallon").

```sql
CREATE TABLE product_variants (
    product_variant_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES products(product_id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL, -- Specific variant name (e.g., "Whole Milk 1 Gallon")
    description TEXT,
    barcode_gtin VARCHAR(14) UNIQUE, -- UPC/EAN code, globally unique if provided
    image_url VARCHAR(255),
    packaging_type product_variant_packaging_type DEFAULT 'single',
    default_unit_of_measure_id UUID REFERENCES unit_of_measures(unit_of_measure_id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ,
    UNIQUE (product_id, name) -- A product cannot have two variants with the same name
);
CREATE INDEX idx_product_variants_product_id ON product_variants(product_id);
CREATE INDEX idx_product_variants_barcode_gtin ON product_variants(barcode_gtin);
CREATE INDEX idx_product_variants_deleted_at ON product_variants(deleted_at);
```

### 4.9. `pantry_locations`
Specific storage locations within a pantry.

```sql
CREATE TABLE pantry_locations (
    pantry_location_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    pantry_id UUID NOT NULL REFERENCES pantries(pantry_id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ,
    UNIQUE (pantry_id, name) -- A pantry cannot have two locations with the same name
);
CREATE INDEX idx_pantry_locations_pantry_id ON pantry_locations(pantry_id);
CREATE INDEX idx_pantry_locations_deleted_at ON pantry_locations(deleted_at);
```

### 4.10. `unit_of_measure_conversions`
Stores conversion factors between units of the same type.

```sql
CREATE TABLE unit_of_measure_conversions (
    conversion_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    from_unit_id UUID NOT NULL REFERENCES unit_of_measures(unit_of_measure_id) ON DELETE CASCADE,
    to_unit_id UUID NOT NULL REFERENCES unit_of_measures(unit_of_measure_id) ON DELETE CASCADE,
    factor DECIMAL(10, 6) NOT NULL, -- Multiplier to convert from_unit to to_unit
    is_bidirectional BOOLEAN NOT NULL DEFAULT TRUE, -- If true, inverse conversion (1/factor) is also valid
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    -- No deleted_at, these are definition tables. Manage by adding/removing rows.
    UNIQUE (from_unit_id, to_unit_id),
    CHECK (from_unit_id <> to_unit_id)
);
```

### 4.11. `stores`
Information about stores where purchases are made.

```sql
CREATE TABLE stores (
    store_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    -- For user-contributed stores, uniqueness needs careful consideration.
    -- Making `name` globally unique might be too restrictive.
    -- Consider UNIQUE(name, created_by_user_id) if stores are user-specific,
    -- or allow duplicates and rely on other fields for distinction.
    -- For now, assuming globally curated stores or app-level handling for user stores.
    -- UNIQUE (name), -- If stores are globally curated and unique
    address VARCHAR(255),
    city VARCHAR(100),
    state_province VARCHAR(100),
    zip_postal_code VARCHAR(20),
    country VARCHAR(100),
    phone_number VARCHAR(20),
    website VARCHAR(255),
    -- created_by_user_id UUID REFERENCES users(user_id), -- If stores can be user-specific
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ -- If stores can be "deleted" from a global list
);
CREATE INDEX idx_stores_name ON stores(name); -- If frequently searched by name
CREATE INDEX idx_stores_deleted_at ON stores(deleted_at);
```

### 4.12. `purchases`
Represents a single shopping trip or receipt.

```sql
CREATE TABLE purchases (
    purchase_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    pantry_id UUID NOT NULL REFERENCES pantries(pantry_id) ON DELETE CASCADE,
    purchase_date TIMESTAMPTZ NOT NULL,
    total_amount DECIMAL(10, 2), -- Total cost of the purchase/receipt
    currency VARCHAR(3) NOT NULL, -- e.g., 'USD', 'EUR'
    store_id UUID REFERENCES stores(store_id) ON DELETE SET NULL, -- Link to a known store
    store_name_override VARCHAR(100), -- For one-off stores or if store_id is not set
    receipt_image_url VARCHAR(255),
    purchased_by_user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE RESTRICT,
    notes TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);
CREATE INDEX idx_purchases_pantry_id ON purchases(pantry_id);
CREATE INDEX idx_purchases_purchase_date ON purchases(purchase_date);
CREATE INDEX idx_purchases_purchased_by_user_id ON purchases(purchased_by_user_id);
CREATE INDEX idx_purchases_deleted_at ON purchases(deleted_at);
```

### 4.13. `purchase_items`
Represents a single line item on a purchase receipt.

```sql
CREATE TABLE purchase_items (
    purchase_item_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    purchase_id UUID NOT NULL REFERENCES purchases(purchase_id) ON DELETE CASCADE,
    product_variant_id UUID NOT NULL REFERENCES product_variants(product_variant_id) ON DELETE RESTRICT,
    quantity_bought DECIMAL(10, 3) NOT NULL,
    unit_of_measure_id UUID NOT NULL REFERENCES unit_of_measures(unit_of_measure_id) ON DELETE RESTRICT,
    price_per_unit DECIMAL(10, 2) NOT NULL,
    total_price_for_item DECIMAL(10, 2) NOT NULL, -- Calculated: quantity_bought * price_per_unit
    notes TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);
CREATE INDEX idx_purchase_items_purchase_id ON purchase_items(purchase_id);
CREATE INDEX idx_purchase_items_product_variant_id ON purchase_items(product_variant_id);
CREATE INDEX idx_purchase_items_deleted_at ON purchase_items(deleted_at);
```

### 4.14. `inventory_items`
Represents a specific quantity of a product variant currently in a pantry.

```sql
CREATE TABLE inventory_items (
    inventory_item_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    pantry_id UUID NOT NULL REFERENCES pantries(pantry_id) ON DELETE CASCADE,
    product_variant_id UUID NOT NULL REFERENCES product_variants(product_variant_id) ON DELETE RESTRICT,
    pantry_location_id UUID REFERENCES pantry_locations(pantry_location_id) ON DELETE SET NULL,
    current_quantity DECIMAL(10, 3) NOT NULL CHECK (current_quantity >= 0),
    unit_of_measure_id UUID NOT NULL REFERENCES unit_of_measures(unit_of_measure_id) ON DELETE RESTRICT,
    expiration_date DATE,
    best_before_date DATE,
    purchase_item_id UUID REFERENCES purchase_items(purchase_item_id) ON DELETE SET NULL, -- Link to original purchase
    original_quantity DECIMAL(10, 3) NOT NULL,
    original_price_per_unit DECIMAL(10, 2),
    currency VARCHAR(3), -- Currency for the original price
    status inventory_item_status NOT NULL DEFAULT 'in_stock',
    notes TEXT,
    added_by_user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE RESTRICT,
    added_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP, -- When item was added to inventory
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);
CREATE INDEX idx_inventory_items_pantry_id ON inventory_items(pantry_id);
CREATE INDEX idx_inventory_items_product_variant_id ON inventory_items(product_variant_id);
CREATE INDEX idx_inventory_items_pantry_location_id ON inventory_items(pantry_location_id);
CREATE INDEX idx_inventory_items_expiration_date ON inventory_items(expiration_date NULLS LAST);
CREATE INDEX idx_inventory_items_status ON inventory_items(status);
CREATE INDEX idx_inventory_items_deleted_at ON inventory_items(deleted_at);
```

### 4.15. `usage_logs`
Tracks when and how much of an `InventoryItem` was consumed.

```sql
CREATE TABLE usage_logs (
    usage_log_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    inventory_item_id UUID NOT NULL REFERENCES inventory_items(inventory_item_id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE RESTRICT,
    quantity_used DECIMAL(10, 3) NOT NULL,
    unit_of_measure_id UUID NOT NULL REFERENCES unit_of_measures(unit_of_measure_id) ON DELETE RESTRICT,
    usage_date TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    notes TEXT, -- E.g., "Used for baking cake."
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
    -- No updated_at or deleted_at, these are immutable logs typically.
);
CREATE INDEX idx_usage_logs_inventory_item_id ON usage_logs(inventory_item_id);
CREATE INDEX idx_usage_logs_user_id ON usage_logs(user_id);
CREATE INDEX idx_usage_logs_usage_date ON usage_logs(usage_date);
```

### 4.16. `shopping_lists`
Allows users to create and manage shopping lists.

```sql
CREATE TABLE shopping_lists (
    shopping_list_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    pantry_id UUID NOT NULL REFERENCES pantries(pantry_id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_by_user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE RESTRICT,
    status shopping_list_status NOT NULL DEFAULT 'active',
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);
CREATE INDEX idx_shopping_lists_pantry_id ON shopping_lists(pantry_id);
CREATE INDEX idx_shopping_lists_created_by_user_id ON shopping_lists(created_by_user_id);
CREATE INDEX idx_shopping_lists_status ON shopping_lists(status);
CREATE INDEX idx_shopping_lists_deleted_at ON shopping_lists(deleted_at);
```

### 4.17. `shopping_list_items`
Individual items on a shopping list.

```sql
CREATE TABLE shopping_list_items (
    shopping_list_item_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    shopping_list_id UUID NOT NULL REFERENCES shopping_lists(shopping_list_id) ON DELETE CASCADE,
    product_variant_id UUID REFERENCES product_variants(variant_id) ON DELETE SET NULL,
    free_text_item_name VARCHAR(255), -- For items not yet linked to a ProductVariant
    quantity_desired DECIMAL(10, 3) NOT NULL,
    unit_of_measure_id UUID REFERENCES unit_of_measures(unit_of_measure_id) ON DELETE SET NULL,
    notes TEXT, -- E.g., "organic only"
    is_purchased BOOLEAN NOT NULL DEFAULT FALSE,
    purchased_on_purchase_id UUID REFERENCES purchases(purchase_id) ON DELETE SET NULL, -- Link to the purchase fulfilling this
    added_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);
CREATE INDEX idx_shopping_list_items_shopping_list_id ON shopping_list_items(shopping_list_id);
CREATE INDEX idx_shopping_list_items_product_variant_id ON shopping_list_items(product_variant_id);
-- Note: Removed tables (unit_of_measure_conversions, pantry_settings, notifications, casbin_rule) as they are not present in migration files
CREATE INDEX idx_shopping_list_items_is_purchased ON shopping_list_items(is_purchased);
CREATE INDEX idx_shopping_list_items_deleted_at ON shopping_list_items(deleted_at);
```

### 4.18. `inventory_adjustments`
Tracks non-consumption changes to `InventoryItems`.

```sql
CREATE TABLE inventory_adjustments (
    adjustment_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    inventory_item_id UUID NOT NULL REFERENCES inventory_items(inventory_item_id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE RESTRICT,
    adjustment_type inventory_adjustment_type NOT NULL,
    quantity_adjusted DECIMAL(10, 3) NOT NULL, -- Amount changed (can be negative)
    unit_of_measure_id UUID NOT NULL REFERENCES unit_of_measures(unit_of_measure_id) ON DELETE RESTRICT,
    reason TEXT,
    adjustment_date TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
    -- No updated_at or deleted_at, these are immutable logs typically.
);
CREATE INDEX idx_inventory_adjustments_inventory_item_id ON inventory_adjustments(inventory_item_id);
CREATE INDEX idx_inventory_adjustments_user_id ON inventory_adjustments(user_id);
CREATE INDEX idx_inventory_adjustments_adjustment_type ON inventory_adjustments(adjustment_type);
```

### 4.19. `recipes`
Stores user-created or pantry-shared recipes.

```sql
CREATE TABLE recipes (
    recipe_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    instructions TEXT NOT NULL,
    servings INT CHECK (servings > 0 OR servings IS NULL),
    prep_time_minutes INT CHECK (prep_time_minutes >= 0 OR prep_time_minutes IS NULL),
    cook_time_minutes INT CHECK (cook_time_minutes >= 0 OR cook_time_minutes IS NULL),
    image_url VARCHAR(255),
    source_url VARCHAR(255), -- Link to original recipe website
    created_by_user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE, -- Recipes deleted if user is deleted
    pantry_id UUID REFERENCES pantries(pantry_id) ON DELETE SET NULL, -- Recipe becomes "unshared" if pantry deleted
    is_public BOOLEAN DEFAULT FALSE, -- For future global sharing
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ
);
CREATE INDEX idx_recipes_created_by_user_id ON recipes(created_by_user_id);
CREATE INDEX idx_recipes_pantry_id ON recipes(pantry_id);
CREATE INDEX idx_recipes_name ON recipes(name); -- For searching recipes
CREATE INDEX idx_recipes_deleted_at ON recipes(deleted_at);
```

### 4.20. `recipe_ingredients`
Details the ingredients required for a specific recipe.

```sql
CREATE TABLE recipe_ingredients (
    recipe_ingredient_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    recipe_id UUID NOT NULL REFERENCES recipes(recipe_id) ON DELETE CASCADE,
    product_variant_id UUID REFERENCES product_variants(product_variant_id) ON DELETE SET NULL,
    free_text_ingredient VARCHAR(255), -- For generic ingredients or if variant not in catalog
    quantity DECIMAL(10, 3) NOT NULL,
    unit_of_measure_id UUID REFERENCES unit_of_measures(unit_of_measure_id) ON DELETE SET NULL,
    notes TEXT, -- E.g., "diced", "warm"
    is_optional BOOLEAN DEFAULT FALSE,
    order_index INT, -- To maintain ingredient order in recipe display
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ -- If ingredients can be soft-deleted from a recipe
);
CREATE INDEX idx_recipe_ingredients_recipe_id ON recipe_ingredients(recipe_id);
CREATE INDEX idx_recipe_ingredients_product_variant_id ON recipe_ingredients(product_variant_id);
CREATE INDEX idx_recipe_ingredients_deleted_at ON recipe_ingredients(deleted_at);
```

### 4.21. `pantry_settings`
Allows defining pantry-specific settings and thresholds.

```sql
CREATE TABLE pantry_settings (
    pantry_setting_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    pantry_id UUID NOT NULL REFERENCES pantries(pantry_id) ON DELETE CASCADE,
    setting_key VARCHAR(100) NOT NULL, -- E.g., 'low_stock_threshold_milk', 'default_currency'
    setting_value TEXT, -- Value of the setting (e.g., '2', 'USD')
    data_type pantry_setting_data_type NOT NULL, -- Helps interpret setting_value
    updated_by_user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE RESTRICT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    -- No deleted_at, manage by adding/updating/removing rows.
    UNIQUE (pantry_id, setting_key)
);
CREATE INDEX idx_pantry_settings_pantry_id ON pantry_settings(pantry_id);
```

### 4.22. `notifications`
Stores user-specific in-app notifications.

```sql
CREATE TABLE notifications (
    notification_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    pantry_id UUID REFERENCES pantries(pantry_id) ON DELETE CASCADE, -- If notification related to a specific pantry
    type notification_type NOT NULL,
    message TEXT NOT NULL,
    related_entity_type VARCHAR(50), -- E.g., 'InventoryItem', 'PantryMembership'
    related_entity_id UUID, -- PK of the related entity
    action_url VARCHAR(255), -- Deep link to navigate to related entity/feature in UI
    is_read BOOLEAN NOT NULL DEFAULT FALSE,
    sent_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMPTZ,
    deleted_at TIMESTAMPTZ -- For users to "delete" notifications from their view
);
CREATE INDEX idx_notifications_user_id_is_read_sent_at ON notifications(user_id, is_read, sent_at DESC);
CREATE INDEX idx_notifications_pantry_id ON notifications(pantry_id);
CREATE INDEX idx_notifications_deleted_at ON notifications(deleted_at);
```

### 4.23. `casbin_rule` (For Casbin GORM Adapter)
This table will be automatically created and managed by the Casbin GORM adapter if used. Its structure is defined by the adapter.

```sql
-- Example structure (the adapter defines this precisely):
-- CREATE TABLE casbin_rule (
--     id SERIAL PRIMARY KEY,
--     ptype VARCHAR(100),
--     v0 VARCHAR(100),
--     v1 VARCHAR(100),
--     v2 VARCHAR(100),
--     v3 VARCHAR(100),
--     v4 VARCHAR(100),
--     v5 VARCHAR(100)
-- );
-- CREATE INDEX idx_casbin_rule ON casbin_rule(ptype, v0, v1); -- Example index
```

## 5. Function to Auto-Update `updated_at` Timestamps

This trigger function automatically updates the `updated_at` column for any table that has it upon an `UPDATE` operation.

```sql
CREATE OR REPLACE FUNCTION trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW(); -- Or CURRENT_TIMESTAMP
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Script to apply the trigger to all relevant tables (execute this after table creation)
DO $$
DECLARE
    t_name TEXT;
    query TEXT;
BEGIN
    FOR t_name IN
        SELECT table_name
        FROM information_schema.columns
        WHERE column_name = 'updated_at'
          AND table_schema = current_schema() -- Or 'public' if tables are in public schema
    LOOP
        query := format(
            'DROP TRIGGER IF EXISTS set_timestamp ON %I; -- Drop if exists to avoid error on re-run
             CREATE TRIGGER set_timestamp
             BEFORE UPDATE ON %I
             FOR EACH ROW
             EXECUTE FUNCTION trigger_set_timestamp();',
            t_name, t_name
        );
        RAISE NOTICE 'Applying trigger to table: %', t_name;
        EXECUTE query;
    END LOOP;
END;
$$ LANGUAGE plpgsql;