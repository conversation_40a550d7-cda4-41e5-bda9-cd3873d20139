# Defensive Error Handling Improvements

## Problem Addressed

The application was experiencing recursive errors in the error handler middleware, where the error handling process itself was causing panics. This created a problematic situation where:

1. An error occurs in the application
2. The error handler tries to process it
3. The error handler itself panics (at line 43 in `error_handler.go`)
4. This creates a recursive error situation

## Stack Trace Analysis

The stack trace showed:
```
github.com/wongpinter/pantry-pal/internal/infra/web.NewServer.ErrorHandler.func1 
(/home/<USER>/Workspaces/go/wongpinter/pantry-pal/internal/infra/web/middleware/error_handler.go:43)
```

Line 43 was calling `buildErrorContext(c, err, config)`, which was trying to access Fiber context properties that might be nil or invalid in certain error scenarios.

## Root Causes

1. **Nil Context Access**: The error handler was trying to access Fiber context properties without checking if the context was valid
2. **Unsafe Context Operations**: Functions like `c.Method()`, `c.Path()`, etc. could panic if called on an invalid context
3. **No Fallback Mechanism**: If context building failed, there was no fallback to prevent the error handler from failing
4. **Recursive Error Potential**: If the error response creation failed, it could trigger another error

## Solution Implemented

### 1. Defensive Error Handler Function

Added comprehensive panic recovery and fallback mechanisms:

```go
return func(c *fiber.Ctx, err error) error {
    // Defensive error handling to prevent recursive errors
    defer func() {
        if r := recover(); r != nil {
            // If error handling itself panics, fall back to basic response
            config.Logger.Error().
                Interface("panic", r).
                Err(err).
                Msg("Error handler panicked, falling back to basic response")
        }
    }()
    
    // Safely get context with recovery
    func() {
        defer func() {
            if r := recover(); r != nil {
                config.Logger.Warn().
                    Interface("panic", r).
                    Msg("Failed to get request context, using defaults")
            }
        }()
        requestID = getRequestIDFromFiberContext(c)
        userID = getUserIDFromFiberContext(c)
        errorContext = buildErrorContext(c, err, config)
    }()
    
    // If context building failed, create minimal context
    if errorContext == nil {
        errorContext = map[string]interface{}{
            "timestamp": time.Now().UTC().Format(time.RFC3339),
            "error":     err.Error(),
        }
    }
    
    // Continue with error processing...
}
```

### 2. Enhanced buildErrorContext Function

Made context building more defensive:

```go
func buildErrorContext(c *fiber.Ctx, err error, config ErrorHandlerConfig) map[string]interface{} {
    // Start with basic context that should always work
    context := map[string]interface{}{
        "timestamp": time.Now().UTC().Format(time.RFC3339),
        "error":     err.Error(),
    }

    // Only try to get Fiber context if c is not nil
    if c == nil {
        return context
    }

    // Safely add Fiber context information
    context["method"] = safelyGetContextValue(c, func() string { return c.Method() }, "UNKNOWN")
    // ... other context values
}
```

### 3. Improved safelyGetContextValue Function

Enhanced the safety mechanism:

```go
func safelyGetContextValue(c *fiber.Ctx, getter func() string, defaultValue string) string {
    defer func() {
        if r := recover(); r != nil {
            // If the getter panics, return default value
        }
    }()
    
    // Check if context is nil
    if c == nil {
        return defaultValue
    }
    
    // Try to get the value
    result := getter()
    if result == "" {
        return defaultValue
    }
    
    return result
}
```

### 4. Defensive buildErrorResponse Function

Added panic recovery for response building:

```go
func buildErrorResponse(c *fiber.Ctx, err error, context map[string]interface{}, config ErrorHandlerConfig) error {
    // Defensive check for nil context
    if c == nil {
        return err
    }

    // Use existing error response handler with panic recovery
    var response error
    func() {
        defer func() {
            if r := recover(); r != nil {
                // If ErrorResponse panics, create a basic response
                response = c.Status(500).JSON(map[string]interface{}{
                    "success": false,
                    "error": map[string]interface{}{
                        "code":    "INTERNAL_ERROR",
                        "message": "Internal server error",
                    },
                })
            }
        }()
        response = handler.ErrorResponse(c, err)
    }()

    // Safely add debug headers with panic recovery
    // ...
}
```

## Benefits of Defensive Error Handling

### 1. Prevents Recursive Errors
- Error handler can no longer cause additional errors
- Graceful fallback to basic error responses
- Maintains application stability

### 2. Comprehensive Logging
- Logs when error handler itself fails
- Provides visibility into error handling issues
- Helps with debugging and monitoring

### 3. Graceful Degradation
- Always provides some form of error response
- Falls back to minimal context when full context fails
- Maintains API contract even in failure scenarios

### 4. Production Stability
- Prevents application crashes due to error handling failures
- Ensures clients always receive proper HTTP responses
- Maintains service availability during error conditions

## Error Handling Hierarchy

1. **Primary**: Full error context with detailed logging
2. **Secondary**: Basic error context with minimal information
3. **Fallback**: Simple error response with generic message
4. **Last Resort**: Basic HTTP 500 response

## Testing Scenarios

The defensive error handling now protects against:

1. **Nil Fiber Context**: When `c` is nil or invalid
2. **Context Access Panics**: When Fiber context methods panic
3. **Header Setting Failures**: When setting response headers fails
4. **Response Building Failures**: When JSON serialization fails
5. **Logger Failures**: When logging operations fail

## Monitoring and Alerting

The enhanced error handling provides better monitoring capabilities:

- **Panic Detection**: Logs when error handler panics occur
- **Context Failures**: Tracks when context building fails
- **Fallback Usage**: Monitors when fallback mechanisms are used
- **Error Categories**: Maintains error categorization for analysis

## Best Practices Applied

1. **Fail Safely**: Always provide some form of response
2. **Log Everything**: Comprehensive logging of all failure modes
3. **Graceful Degradation**: Fallback to simpler responses when needed
4. **Defensive Programming**: Assume any operation can fail
5. **Recovery Mechanisms**: Use panic recovery at multiple levels

This defensive error handling ensures that the error handler itself never becomes a source of application instability, while maintaining comprehensive error logging and proper client responses.
