# Generic Error Responses for Security

## Security Enhancement Implemented

To prevent information disclosure and improve security, the API error responses have been modified to return generic error messages instead of detailed error information that could be exploited by attackers.

## Problem Addressed

**Before**: API responses contained detailed error information that could expose:
- Database schema details
- Internal system paths
- Validation field names and rules
- Business logic details
- Infrastructure information
- User enumeration data

**Example of problematic response**:
```json
{
  "success": false,
  "error": {
    "code": "NOT_FOUND",
    "message": "User with ID 12345 not found in database table users",
    "details": {
      "user_id": "12345",
      "table": "users",
      "query": "SELECT * FROM users WHERE id = ?",
      "database": "pantrypal"
    }
  }
}
```

## Solution Implemented

**After**: API responses now return generic, safe error messages:

```json
{
  "success": false,
  "error": {
    "code": "NOT_FOUND",
    "message": "Resource not found"
  },
  "timestamp": "2025-06-11T16:42:06Z",
  "request_id": "667d92e5-4cbd-4183-9eae-f7e388bdec0b"
}
```

## Changes Made

### 1. Updated Error Response Functions

**File**: `internal/infra/web/handler/response.go`

#### ErrorResponse Function
- Removed `Details` field from error responses
- Added `getGenericErrorMessage()` function to map HTTP status codes to generic messages
- Maintains error codes for client-side error handling while hiding implementation details

#### Generic Error Messages by HTTP Status:
- **400**: "Bad request"
- **401**: "Authentication required"
- **403**: "Access denied"
- **404**: "Resource not found"
- **409**: "Resource conflict"
- **422**: "Invalid input data"
- **429**: "Too many requests"
- **500**: "Internal server error"
- **502**: "Service temporarily unavailable"
- **503**: "Service unavailable"
- **504**: "Request timeout"

#### Specific Error Code Messages:
- **VALIDATION_FAILED**: "Validation failed"
- **INVALID_CREDENTIALS**: "Invalid credentials"
- **TOKEN_EXPIRED**: "Session expired"
- **TOKEN_INVALID**: "Invalid session"
- **BUSINESS_RULE_VIOLATION**: "Operation not allowed"
- **QUOTA_EXCEEDED**: "Quota exceeded"

### 2. Updated Helper Functions

#### ValidationErrorResponse
- Removed field-specific validation error details
- Returns generic "Validation failed" message

#### BadRequest Function
- Ignores detailed message parameter
- Returns generic "Bad request" message

#### Conflict Function
- Ignores detailed message parameter
- Returns generic "Resource conflict" message

### 3. Enhanced Error Handler Middleware

**File**: `internal/infra/web/middleware/error_handler.go`

- Detailed error information is still logged for debugging
- Debug headers are only added in development mode
- Response body contains only generic messages

## Security Benefits

### 1. Information Disclosure Prevention
- **Database Schema**: No table names, column names, or query details exposed
- **File Paths**: No internal system paths revealed
- **Business Logic**: No internal business rules or validation details exposed
- **Infrastructure**: No database hosts, connection details, or internal service names revealed

### 2. Attack Surface Reduction
- **SQL Injection**: Attackers can't see database structure or queries
- **User Enumeration**: Generic messages prevent user existence confirmation
- **System Reconnaissance**: No internal system information leaked
- **Validation Bypass**: No validation rule details to exploit

### 3. Compliance Improvement
- **OWASP Guidelines**: Follows OWASP recommendations for error handling
- **Security Standards**: Aligns with security best practices
- **Privacy Protection**: Reduces risk of exposing sensitive user data

## Development vs Production

### Development Mode
- **Debug Headers**: Additional debugging information in HTTP headers
  - `X-Debug-Stack-Trace`: Stack trace for debugging
  - `X-Debug-Error-Category`: Error categorization
  - `X-Debug-Request-ID`: Request correlation ID
- **Detailed Logging**: Full error context logged to application logs
- **Response Body**: Still generic for consistency

### Production Mode
- **No Debug Headers**: Clean responses without debugging information
- **Detailed Logging**: Full error context still logged for operations team
- **Generic Responses**: Only safe, generic error messages

## Logging Preservation

**Important**: While API responses are now generic, detailed error information is still:
- ✅ **Logged with full context** for debugging and monitoring
- ✅ **Categorized by error type** for better analysis
- ✅ **Correlated with request IDs** for tracing
- ✅ **Available to operations teams** through log aggregation
- ✅ **Structured for monitoring** and alerting systems

## Testing Results

### Before (Detailed Errors):
```bash
curl /test/notfound
# Response: "User with ID 12345 not found in database table users"
```

### After (Generic Errors):
```bash
curl /test/notfound
# Response: "Resource not found"
```

### Error Code Preservation:
Error codes are still returned for programmatic error handling:
- `NOT_FOUND` - Client can handle resource not found scenarios
- `VALIDATION_FAILED` - Client can show validation error UI
- `FORBIDDEN` - Client can redirect to login or show access denied
- `INTERNAL_ERROR` - Client can show generic error message

## Implementation Notes

### 1. Backward Compatibility
- Error codes remain the same for client compatibility
- Response structure unchanged (only message content is generic)
- HTTP status codes preserved for proper REST semantics

### 2. Client-Side Error Handling
Clients should handle errors based on:
- **HTTP Status Codes**: For general error categorization
- **Error Codes**: For specific error handling logic
- **Generic Messages**: For user-friendly error display

### 3. Monitoring and Debugging
- Use log aggregation tools to access detailed error information
- Correlate errors using request IDs
- Monitor error patterns using error categories
- Set up alerts based on error rates and types

## Best Practices Applied

1. **Fail Securely**: Default to generic messages when in doubt
2. **Log Everything**: Maintain detailed logs for operations
3. **Minimize Exposure**: Only expose what's necessary for client functionality
4. **Consistent Messaging**: Use standardized generic messages
5. **Environment Awareness**: Different behavior for development vs production

This security enhancement significantly reduces the attack surface while maintaining the detailed error logging needed for debugging and monitoring.
